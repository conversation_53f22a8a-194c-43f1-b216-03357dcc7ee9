@echo off
echo 🐳 Testing Docker Compose deployment...

echo Stopping existing containers...
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

echo Cleaning up old images...
docker system prune -f

echo Building and starting services...
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

echo Waiting for services to start...
timeout /t 30 /nobreak

echo Checking service status...
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps

echo Backend logs:
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs backend --tail=10

echo Database logs:
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs postgres --tail=5

echo Testing health endpoint...
curl -f http://localhost:3000/health

echo ✅ Docker deployment test completed!
pause
