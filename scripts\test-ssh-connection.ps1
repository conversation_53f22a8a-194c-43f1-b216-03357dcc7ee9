# Test SSH Connection Script for ChanguApp
# This script tests the SSH connection using the same parameters as the GitHub Actions workflow

param(
    [switch]$Verbose = $false
)

# Configuration (same as in GitHub Actions)
$EC2_HOST = "ec2-54-233-23-129.sa-east-1.compute.amazonaws.com"
$EC2_USER = "ec2-user"
$SSH_KEY = ".\backend\keys\tuchanga.pem"
$SSH_OPTS = @(
    "-i", $SSH_KEY,
    "-o", "ConnectTimeout=30",
    "-o", "StrictHostKeyChecking=no",
    "-o", "ServerAliveInterval=60",
    "-o", "ServerAliveCountMax=3",
    "-o", "TCPKeepAlive=yes"
)

Write-Host "🔍 Testing SSH Connection to ChanguApp EC2 Instance" -ForegroundColor Blue
Write-Host "=================================================="

# Check if SSH key exists
if (-not (Test-Path $SSH_KEY)) {
    Write-Host "❌ SSH key not found at $SSH_KEY" -ForegroundColor Red
    Write-Host "   Make sure you're running this from the project root directory" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ SSH key found: $SSH_KEY" -ForegroundColor Green
Write-Host "🎯 Target: $EC2_USER@$EC2_HOST" -ForegroundColor Blue

# Test basic connectivity
Write-Host "`n🔍 Testing basic connectivity..." -ForegroundColor Blue
try {
    $ping = Test-Connection -ComputerName $EC2_HOST -Count 1 -Quiet
    if ($ping) {
        Write-Host "✅ Host is reachable via ping" -ForegroundColor Green
    } else {
        Write-Host "❌ Host is not reachable via ping" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Ping failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test SSH connection
Write-Host "`n🔍 Testing SSH connection..." -ForegroundColor Blue
$sshTestArgs = $SSH_OPTS + @("$EC2_USER@$EC2_HOST", "echo 'SSH connection successful'")

try {
    if ($Verbose) {
        Write-Host "Command: ssh $($sshTestArgs -join ' ')" -ForegroundColor Gray
    }
    
    $result = & ssh @sshTestArgs 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ SSH connection successful" -ForegroundColor Green
        Write-Host "   Response: $result" -ForegroundColor Gray
    } else {
        Write-Host "❌ SSH connection failed (exit code: $LASTEXITCODE)" -ForegroundColor Red
        
        if ($Verbose) {
            Write-Host "`n🔍 Verbose SSH output:" -ForegroundColor Yellow
            $verboseArgs = @("-v") + $SSH_OPTS + @("$EC2_USER@$EC2_HOST", "echo 'test'")
            & ssh @verboseArgs
        }
        exit 1
    }
} catch {
    Write-Host "❌ SSH test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test system information retrieval
Write-Host "`n🔍 Testing system information retrieval..." -ForegroundColor Blue
$systemTestArgs = $SSH_OPTS + @("$EC2_USER@$EC2_HOST", "uptime && echo 'System info test successful'")

try {
    $systemInfo = & ssh @systemTestArgs 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ System information retrieval successful" -ForegroundColor Green
        Write-Host "   $systemInfo" -ForegroundColor Gray
    } else {
        Write-Host "⚠️  System information retrieval failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not retrieve system information" -ForegroundColor Yellow
}

# Test service status check
Write-Host "`n🔍 Testing service status check..." -ForegroundColor Blue
$serviceTestArgs = $SSH_OPTS + @("$EC2_USER@$EC2_HOST", "sudo systemctl is-active changuapp-backend nginx postgresql")

try {
    $serviceStatus = & ssh @serviceTestArgs 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Service status check successful" -ForegroundColor Green
        $services = $serviceStatus -split "`n"
        Write-Host "   Backend: $($services[0])" -ForegroundColor Gray
        Write-Host "   Nginx: $($services[1])" -ForegroundColor Gray
        Write-Host "   PostgreSQL: $($services[2])" -ForegroundColor Gray
    } else {
        Write-Host "⚠️  Service status check failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not check service status" -ForegroundColor Yellow
}

# Test file operations (similar to rsync)
Write-Host "`n🔍 Testing file operations..." -ForegroundColor Blue
$fileTestArgs = $SSH_OPTS + @("$EC2_USER@$EC2_HOST", "ls -la /home/<USER>/tuchanga/ | head -5")

try {
    $fileList = & ssh @fileTestArgs 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ File operations successful" -ForegroundColor Green
        Write-Host "   Directory listing:" -ForegroundColor Gray
        $fileList | ForEach-Object { Write-Host "     $_" -ForegroundColor Gray }
    } else {
        Write-Host "⚠️  File operations failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not perform file operations" -ForegroundColor Yellow
}

Write-Host "`n🎉 SSH connection test completed!" -ForegroundColor Green
Write-Host "✅ Your SSH connection is working properly" -ForegroundColor Green
Write-Host "`n💡 Tips:" -ForegroundColor Blue
Write-Host "   - Use -Verbose for detailed SSH output" -ForegroundColor Gray
Write-Host "   - Run .\scripts\Fix-SSHConnections.ps1 if you encounter issues" -ForegroundColor Gray
Write-Host "   - Check AWS console if the instance is unresponsive" -ForegroundColor Gray
