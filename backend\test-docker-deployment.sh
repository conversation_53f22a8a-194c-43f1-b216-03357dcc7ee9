#!/bin/bash

echo "🐳 Testing Docker Compose deployment..."

# Stop any existing containers
echo "Stopping existing containers..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

# Remove old images
echo "Cleaning up old images..."
docker system prune -f

# Build and start services
echo "Building and starting services..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Wait for services to start
echo "Waiting for services to start..."
sleep 30

# Check status
echo "Checking service status..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps

# Check logs
echo "Backend logs:"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs backend --tail=10

echo "Database logs:"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs postgres --tail=5

# Test health endpoint
echo "Testing health endpoint..."
curl -f http://localhost:3000/health || echo "Health check failed"

echo "✅ Docker deployment test completed!"
