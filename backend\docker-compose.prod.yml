version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    environment:
      POSTGRES_DB: job_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ChanguApp2024SecureDB
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d

  # NestJS Backend API with PostgreSQL
  backend:
    environment:
      NODE_ENV: production
      HOST: 0.0.0.0
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: ChanguApp2024SecureDB
      DB_DATABASE: job_platform
      DATABASE_URL: *********************************************************/job_platform
      JWT_SECRET: ChanguApp2024SuperSecretJWTKeyChangeInProduction
      JWT_EXPIRES_IN: "24h"
      FIREBASE_PROJECT_ID: tu-changa-583b3
      CORS_ORIGIN: "*"
      API_PREFIX: ""
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  job-platform-network:
    driver: bridge
